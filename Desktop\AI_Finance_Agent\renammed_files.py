import os
import re
import arxiv
import time
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from threading import Thread

def sanitize_filename(title):
    # Remove invalid characters for filenames
    sanitized = re.sub(r'[\\/*?:"<>|]', "", title)
    # Replace spaces with underscores
    sanitized = sanitized.replace(' ', '_')
    # Limit filename length (Windows has a 255 character path limit)
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    return sanitized

def extract_arxiv_id(filename):
    # Try to extract arXiv ID from filename
    # Pattern for arXiv IDs (both old and new format)
    arxiv_pattern = r'((?:\d{4}\.\d{4,5})|(?:[a-z\-]+(?:\.[A-Z]{2})?\/\d{7})(?:v\d+)?)'
    match = re.search(arxiv_pattern, filename)
    
    if match:
        return match.group(1)
    return None

def rename_papers_in_folder(folder_path, update_callback, completion_callback):
    if not os.path.exists(folder_path):
        update_callback(f"Directory '{folder_path}' not found.")
        completion_callback(0, 0, [])
        return
    
    # Get all PDF files in the selected directory
    pdf_files = [f for f in os.listdir(folder_path) if f.endswith('.pdf')]
    update_callback(f"Found {len(pdf_files)} PDF files to process.")
    
    # Track successful and failed renames
    success_count = 0
    failed_ids = []
    
    for i, pdf_file in enumerate(pdf_files):
        # Update progress
        progress_percent = int((i / len(pdf_files)) * 100)
        update_callback(f"Processing file {i+1} of {len(pdf_files)}: {pdf_file}", progress_percent)
        
        # Extract arXiv ID from filename
        arxiv_id = extract_arxiv_id(pdf_file)
        
        if not arxiv_id:
            update_callback(f"Could not extract arXiv ID from filename: {pdf_file}")
            failed_ids.append(pdf_file)
            continue
        
        try:
            # Fetch paper metadata from arXiv
            search = arxiv.Search(id_list=[arxiv_id])
            paper = next(search.results())
            
            # Generate new filename using paper title
            new_filename = sanitize_filename(paper.title) + '.pdf'
            old_path = os.path.join(folder_path, pdf_file)
            new_path = os.path.join(folder_path, new_filename)
            
            # Rename file
            if os.path.exists(new_path) and old_path != new_path:
                update_callback(f"File with name '{new_filename}' already exists. Skipping {pdf_file}.")
                failed_ids.append(pdf_file)
                continue
                
            os.rename(old_path, new_path)
            update_callback(f"Renamed: {pdf_file} -> {new_filename}")
            success_count += 1
            
            # Sleep to avoid hitting API rate limits
            time.sleep(1)
            
        except Exception as e:
            update_callback(f"Error processing {pdf_file}: {e}")
            failed_ids.append(pdf_file)
    
    # Call completion callback with results
    completion_callback(success_count, len(pdf_files) - success_count, failed_ids)

class ArxivRenamerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("arXiv Paper Renamer")
        self.root.geometry("600x500")
        self.root.minsize(500, 400)
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Folder selection
        folder_frame = ttk.Frame(main_frame)
        folder_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(folder_frame, text="Select folder containing PDF files:").pack(side=tk.LEFT, padx=5)
        
        self.folder_path = tk.StringVar()
        folder_entry = ttk.Entry(folder_frame, textvariable=self.folder_path, width=50)
        folder_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_button = ttk.Button(folder_frame, text="Browse...", command=self.browse_folder)
        browse_button.pack(side=tk.LEFT, padx=5)
        
        # Start button
        self.start_button = ttk.Button(main_frame, text="Start Renaming", command=self.start_renaming)
        self.start_button.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, orient=tk.HORIZONTAL, length=100, mode='determinate')
        self.progress.pack(fill=tk.X, pady=5)
        
        # Output log
        log_frame = ttk.LabelFrame(main_frame, text="Log")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # Disable text editing
        self.log_text.config(state=tk.DISABLED)
        
        # Thread for processing
        self.processing_thread = None
        
    def browse_folder(self):
        folder_selected = filedialog.askdirectory(title="Select folder containing PDF files")
        if folder_selected:
            self.folder_path.set(folder_selected)
    
    def update_log(self, message, progress=None):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        if progress is not None:
            self.progress['value'] = progress
        
        # Update the UI
        self.root.update_idletasks()
    
    def start_renaming(self):
        folder = self.folder_path.get()
        if not folder or not os.path.isdir(folder):
            messagebox.showerror("Error", "Please select a valid folder.")
            return
        
        # Clear log and reset progress
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.progress['value'] = 0
        
        # Disable start button during processing
        self.start_button.config(state=tk.DISABLED)
        
        # Start processing in a separate thread
        self.update_log(f"Starting to process files in: {folder}")
        self.processing_thread = Thread(
            target=rename_papers_in_folder,
            args=(folder, self.update_log, self.process_completed)
        )
        self.processing_thread.daemon = True
        self.processing_thread.start()
    
    def process_completed(self, success_count, failed_count, failed_ids):
        self.progress['value'] = 100
        self.update_log("\nRename process completed.")
        self.update_log(f"Successfully renamed: {success_count} files")
        self.update_log(f"Failed to rename: {failed_count} files")
        
        if failed_ids:
            self.update_log("\nFiles that couldn't be renamed:")
            for f in failed_ids:
                self.update_log(f"- {f}")
        
        # Re-enable start button
        self.start_button.config(state=tk.NORMAL)
        
        # Show completion message
        messagebox.showinfo("Process Complete", 
                           f"Renaming complete!\n\nSuccessfully renamed: {success_count} files\nFailed to rename: {failed_count} files")

if __name__ == "__main__":
    try:
        import arxiv
    except ImportError:
        print("The 'arxiv' package is required. Installing...")
        import subprocess
        subprocess.check_call(["pip", "install", "arxiv"])
        import arxiv
    
    root = tk.Tk()
    app = ArxivRenamerApp(root)
    root.mainloop()