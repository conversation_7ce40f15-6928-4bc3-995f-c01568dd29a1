import os
from pdfminer.high_level import extract_text

def extract_text_from_pdf(pdf_path):
    try:
        text = extract_text(pdf_path)
        return text
    except Exception as e:
        print(f"Error processing {pdf_path}: {e}")
        return ""

paper_texts = []
for filename in os.listdir("papers"):
    if filename.endswith(".pdf"):
        pdf_path = os.path.join("papers", filename)
        text = extract_text_from_pdf(pdf_path)
        if text:
            paper_texts.append(text)

with open("paper_texts.txt", "w", encoding="utf-8") as f:
    for text in paper_texts:
        f.write(text + "\n---\n")