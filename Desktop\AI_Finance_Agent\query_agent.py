from transformers import pipeline
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np
import pickle
import os
import PyPDF2

# Load resources
general_qa = pipeline("question-answering")
finance_qa = pipeline("question-answering", model="yiyanghkust/finbert-tone")
model = SentenceTransformer('all-MiniLM-L6-v2')

# Check if index exists, if not create it
if not os.path.exists("paper_index.faiss") or not os.path.exists("paper_texts.pkl"):
    print("Building paper index...")
    build_paper_index()

# Load index and texts
index = faiss.read_index("paper_index.faiss")
with open("paper_texts.pkl", "rb") as f:
    paper_texts = pickle.load(f)

def build_paper_index():
    """Build a FAISS index from all papers in the papers directory."""
    PAPERS_DIR = r"C:\Users\<USER>\OneDrive - Spectra Asset Management\Desktop\AI_Finance_Agent\papers"
    texts = []
    paper_paths = []
    
    # Walk through all subdirectories
    for root, dirs, files in os.walk(PAPERS_DIR):
        for file in files:
            if file.endswith('.pdf'):
                file_path = os.path.join(root, file)
                try:
                    # Extract text from PDF
                    with open(file_path, 'rb') as f:
                        reader = PyPDF2.PdfReader(f)
                        paper_text = ""
                        for page in reader.pages:
                            paper_text += page.extract_text() + "\n"
                    
                    # Split into chunks to avoid token limits and maintain context
                    chunks = [paper_text[i:i+1000] for i in range(0, len(paper_text), 1000)]
                    for chunk in chunks:
                        if len(chunk.strip()) > 100:  # Only add substantial chunks
                            texts.append(chunk)
                            paper_paths.append(file_path)
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
    
    # Create embeddings
    embeddings = model.encode(texts)
    
    # Build FAISS index
    dimension = embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    index.add(np.array(embeddings).astype('float32'))
    
    # Save index and texts
    faiss.write_index(index, "paper_index.faiss")
    with open("paper_texts.pkl", "wb") as f:
        pickle.dump(texts, f)
    
    # Save paper paths for reference
    with open("paper_paths.pkl", "wb") as f:
        pickle.dump(paper_paths, f)
    
    print(f"Index built with {len(texts)} text chunks from papers.")

def is_finance_query(query):
    """Check if the query is finance-related."""
    finance_keywords = [
        "finance", "stock", "market", "investment", "bank", "economy", "portfolio",
        "asset", "fund", "hedge", "trading", "strategy", "risk", "return", "alpha",
        "beta", "sharpe", "volatility", "derivative", "option", "bond", "equity"
    ]
    return any(keyword in query.lower() for keyword in finance_keywords)

def answer_query(query, k=5):
    """Answer a query using the appropriate model and retrieved context."""
    # Retrieve passages
    query_embedding = model.encode([query])
    D, I = index.search(np.array(query_embedding), k)
    retrieved_passages = [paper_texts[i] for i in I[0]]
    combined_context = " ".join(retrieved_passages)

    # Select the appropriate model
    if is_finance_query(query):
        qa_model = finance_qa
        print("Using finance-specific model...")
    else:
        qa_model = general_qa
        print("Using general model...")

    # Generate answer
    result = qa_model(question=query, context=combined_context)
    
    # Enhance answer with source information if available
    try:
        with open("paper_paths.pkl", "rb") as f:
            paper_paths = pickle.load(f)
        
        sources = set()
        for i in I[0]:
            if i < len(paper_paths):
                paper_path = paper_paths[i]
                paper_name = os.path.basename(paper_path)
                sources.add(paper_name)
        
        source_info = "\n\nSources: " + ", ".join(sources) if sources else ""
        return result['answer'] + source_info
    except:
        return result['answer']

def suggest_strategy(query):
    """Suggest investment strategies based on the query and paper knowledge."""
    # First, get relevant context from papers
    query_embedding = model.encode([query])
    D, I = index.search(np.array(query_embedding), 7)  # Get more context for strategy suggestions
    retrieved_passages = [paper_texts[i] for i in I[0]]
    combined_context = " ".join(retrieved_passages)
    
    # Create a strategy-focused prompt
    strategy_prompt = f"""
    Based on the following research context and the query "{query}", suggest practical investment strategies:
    
    Context: {combined_context[:3000]}...
    
    Provide specific, actionable investment strategy recommendations that address the query.
    """
    
    # Use the finance model for strategy suggestions
    result = finance_qa(question=strategy_prompt, context=combined_context[:5000])
    return result['answer']

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python query_agent.py \"Your question here\"")
        print("Or: python query_agent.py --strategy \"Your strategy question here\"")
        sys.exit(1)
    
    if sys.argv[1] == "--strategy":
        if len(sys.argv) < 3:
            print("Please provide a strategy question")
            sys.exit(1)
        query = sys.argv[2]
        answer = suggest_strategy(query)
        print(f"Strategy Recommendation: {answer}")
    else:
        query = sys.argv[1]
        answer = answer_query(query)
    print(f"Answer: {answer}")
