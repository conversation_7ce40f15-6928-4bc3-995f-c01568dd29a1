<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Finance Papers Library</title>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #1d1d1f;
            --secondary: #06c;
            --accent: #2997ff;
            --light: #f5f5f7;
            --dark: #1d1d1f;
            --text: #1d1d1f;
            --text-secondary: #86868b;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --background-gradient: linear-gradient(180deg, #fbfbfd 0%, #f5f5f7 100%);
        }
        
        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--light);
            color: var(--text);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
        }
        
        .container {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }
        
        .sidebar {
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: var(--text);
            padding: 25px;
            overflow-y: auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            border-right: 1px solid rgba(0,0,0,0.1);
        }
        
        .sidebar-header {
            margin-bottom: 30px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            padding-bottom: 20px;
        }
        
        .logo {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .logo i {
            margin-right: 10px;
            color: var(--secondary);
        }
        
        .content {
            padding: 40px;
            overflow-y: auto;
            background: var(--background-gradient);
        }
        
        h1, h2, h3 {
            margin-top: 0;
            font-weight: 600;
        }
        
        .search-box {
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 25px;
            border: none;
            border-radius: 8px;
            box-sizing: border-box;
            background-color: rgba(0,0,0,0.05);
            color: var(--text);
            font-family: inherit;
            transition: all 0.3s;
        }
        
        .search-box::placeholder {
            color: var(--text-secondary);
        }
        
        .search-box:focus {
            outline: none;
            background-color: rgba(0,0,0,0.08);
            box-shadow: 0 0 0 2px var(--secondary);
        }
        
        .category-filter {
            margin-bottom: 25px;
        }
        
        .category-filter label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .category-filter select {
            background-color: rgba(0,0,0,0.05);
            color: var(--text);
            border: none;
            width: 100%;
            border-radius: 8px;
            padding: 12px 15px;
            font-family: inherit;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2386868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        
        .paper-dropdown {
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 15px;
            border: none;
            border-radius: 8px;
            background-color: rgba(0,0,0,0.05);
            color: var(--text);
            font-family: inherit;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2386868b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }
        
        .paper-details {
            background-color: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 18px;
            padding: 35px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.05);
        }
        
        .paper-title {
            color: var(--text);
            margin-bottom: 20px;
            padding-bottom: 15px;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: -0.02em;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .paper-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .paper-meta-item {
            display: flex;
            align-items: center;
        }
        
        .paper-meta-item i {
            margin-right: 8px;
            color: var(--secondary);
        }
        
        .paper-summary {
            background-color: rgba(0,0,0,0.03);
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            line-height: 1.6;
        }
        
        .paper-summary h3 {
            margin-top: 0;
            font-size: 20px;
            margin-bottom: 15px;
            color: var(--text);
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 22px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: inherit;
            font-size: 14px;
        }
        
        .btn i {
            margin-right: 8px;
        }
        
        .btn-primary {
            background-color: var(--secondary);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--accent);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: rgba(0,0,0,0.05);
            color: var(--text);
        }
        
        .btn-secondary:hover {
            background-color: rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .llm-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 18px;
            padding: 35px;
            width: 500px;
            max-width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .modal-content h2 {
            margin-top: 0;
            color: var(--text);
            padding-bottom: 15px;
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        .llm-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 25px 0;
        }
        
        .llm-option {
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }
        
        .llm-option:hover {
            border-color: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.05);
        }
        
        .llm-option.selected {
            border-color: var(--secondary);
            background-color: rgba(0, 108, 204, 0.05);
        }
        
        .llm-option h3 {
            margin: 0 0 8px 0;
            color: var(--text);
        }
        
        .llm-option p {
            margin: 0;
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-top: 10px;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .status-indicator .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--success);
            margin-right: 8px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            flex-direction: column;
        }
        
        .spinner {
            border: 4px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 4px solid var(--accent);
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        .loading-text {
            color: white;
            font-size: 18px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            z-index: 1500;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s;
        }
        
        .toast.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .toast i {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .toast.success {
            background-color: var(--success);
        }
        
        .toast.error {
            background-color: var(--danger);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>AI Finance Papers</span>
                </div>
                <p style="font-size: 13px; opacity: 0.7; margin-top: 5px;">Your research intelligence platform</p>
            </div>
            
            <input type="text" class="search-box" placeholder="Search papers..." id="searchBox">
            
            <div class="category-filter">
                <label>Filter by Category</label>
                <select id="categoryFilter" class="paper-dropdown">
                    <option value="all">All Categories</option>
                    <!-- Categories will be populated dynamically -->
                </select>
            </div>
            
            <div class="paper-select-container">
                <label>Select Paper</label>
                <select id="paperDropdown" class="paper-dropdown">
                    <option value="">Choose a paper...</option>
                    <!-- Papers will be populated dynamically -->
                </select>
            </div>
            
            <div class="sidebar-footer">
                <button id="downloadPapersBtn" class="btn btn-primary" style="width: 100%; margin-bottom: 10px;">
                    <i class="fas fa-download"></i> Download New Papers
                </button>
                <button id="reorganizePapersBtn" class="btn btn-secondary" style="width: 100%; margin-bottom: 10px;">
                    <i class="fas fa-folder-tree"></i> Reorganize Papers
                </button>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>Server running</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <div id="paperDetails" class="paper-details">
                <h1 class="paper-title">Welcome to AI Finance Papers Library</h1>
                
                <div class="paper-summary">
                    <h3>About this platform</h3>
                    <p>This platform helps you discover, analyze, and process the latest research papers in AI and Finance. Use the sidebar to browse papers, and select any paper to view its details.</p>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" id="startServerBtn">
                        <i class="fas fa-play"></i> Start Server
                    </button>
                    <button class="btn btn-secondary" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i> Refresh Papers
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="llm-modal" id="llmModal">
        <div class="modal-content">
            <h2>Process with LLM</h2>
            <p>Select an AI model to process this paper:</p>
            
            <div class="llm-options">
                <div class="llm-option" data-llm="claude">
                    <h3>Claude</h3>
                    <p>Anthropic's Claude</p>
                </div>
                <div class="llm-option" data-llm="gpt">
                    <h3>ChatGPT</h3>
                    <p>OpenAI's GPT</p>
                </div>
                <div class="llm-option" data-llm="grok">
                    <h3>Grok</h3>
                    <p>xAI's Grok</p>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary" id="processBtn">Process Paper</button>
                <button class="btn btn-secondary" id="cancelBtn">Cancel</button>
            </div>
        </div>
    </div>
    
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
        <div class="loading-text" id="loadingText">Processing...</div>
    </div>
    
    <div class="toast" id="toast">
        <i class="fas fa-check-circle"></i>
        <span id="toastMessage">Operation successful</span>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let serverRunning = false;
            let papers = [];
            let selectedPaper = null;
            let selectedLLM = null;
            
            // DOM elements
            const paperDropdown = document.getElementById('paperDropdown');
            const categoryFilter = document.getElementById('categoryFilter');
            const searchBox = document.getElementById('searchBox');
            const paperDetails = document.getElementById('paperDetails');
            const startServerBtn = document.getElementById('startServerBtn');
            const refreshBtn = document.getElementById('refreshBtn');
            const downloadPapersBtn = document.getElementById('downloadPapersBtn');
            const reorganizePapersBtn = document.getElementById('reorganizePapersBtn');
            const llmModal = document.getElementById('llmModal');
            const llmOptions = document.querySelectorAll('.llm-option');
            const processBtn = document.getElementById('processBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loadingText = document.getElementById('loadingText');
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toastMessage');
            
            // Load papers from server
            function loadPapers() {
                fetch('/get_papers')
                    .then(response => response.json())
                    .then(data => {
                        papers = data;
                        updatePaperDropdown();
                        loadCategories();
                    })
                    .catch(error => {
                        console.error('Error loading papers:', error);
                        showToast('Failed to load papers', 'error');
                    });
            }
            
            // Load categories
            function loadCategories() {
                fetch('/get_categories')
                    .then(response => response.json())
                    .then(categories => {
                        // Clear existing options except "All Categories"
                        while (categoryFilter.options.length > 1) {
                            categoryFilter.remove(1);
                        }
                        
                        // Add categories
                        categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category;
                            option.textContent = category;
                            categoryFilter.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error loading categories:', error);
                    });
            }
            
            // Update paper dropdown based on filters
            function updatePaperDropdown() {
                // Clear existing options except the placeholder
                while (paperDropdown.options.length > 1) {
                    paperDropdown.remove(1);
                }
                
                // Get filter values
                const categoryValue = categoryFilter.value;
                const searchValue = searchBox.value.toLowerCase();
                
                // Filter papers
                const filteredPapers = papers.filter(paper => {
                    const matchesCategory = categoryValue === 'all' || paper.category === categoryValue;
                    const matchesSearch = paper.title.toLowerCase().includes(searchValue);
                    return matchesCategory && matchesSearch;
                });
                
                // Add filtered papers to dropdown
                filteredPapers.forEach(paper => {
                    const option = document.createElement('option');
                    option.value = paper.id;
                    option.textContent = paper.title;
                    paperDropdown.appendChild(option);
                });
            }
            
            // Display paper details
            function displayPaperDetails(paper) {
                selectedPaper = paper;
                
                // Create HTML for paper details
                const html = `
                    <h1 class="paper-title">${paper.title}</h1>
                    
                    <div class="paper-meta">
                        <div class="paper-meta-item">
                            <i class="fas fa-folder"></i>
                            <span>${paper.category}</span>
                        </div>
                        <div class="paper-meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>${paper.published}</span>
                        </div>
                    </div>
                    
                    ${paper.summary ? `
                    <div class="paper-summary">
                        <h3>Summary</h3>
                        <p>${paper.summary}</p>
                    </div>
                    ` : ''}
                    
                    <div class="action-buttons">
                        <button class="btn btn-primary" id="viewPdfBtn">
                            <i class="fas fa-file-pdf"></i> View PDF
                        </button>
                        <button class="btn btn-secondary" id="generateSummaryBtn">
                            <i class="fas fa-file-alt"></i> Generate Summary
                        </button>
                        <button class="btn btn-secondary" id="analyzeForHedgeFundBtn">
                            <i class="fas fa-chart-line"></i> Analyze for Fund Strategy
                        </button>
                        <button class="btn btn-secondary" id="processWithLLMBtn">
                            <i class="fas fa-robot"></i> Process with LLM
                        </button>
                        <button class="btn btn-secondary" id="downloadBtn">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                `;
                
                paperDetails.innerHTML = html;
                
                // Add event listeners to buttons
                document.getElementById('viewPdfBtn')?.addEventListener('click', () => {
                    window.open(paper.path, '_blank');
                });

                document.getElementById('generateSummaryBtn')?.addEventListener('click', () => {
                    showLoading('Generating summary...');
                    
                    fetch(`/generate_summary/${paper.id}`)
                        .then(response => response.json())
                        .then(data => {
                            hideLoading();
                            if (data.success) {
                                // Update the paper object with the summary
                                paper.summary = data.summary;
                                
                                // Refresh the paper details to show the summary
                                displayPaperDetails(paper);
                                showToast('Summary generated successfully');
                            } else {
                                showToast('Failed to generate summary: ' + data.error, 'error');
                            }
                        })
                        .catch(error => {
                            hideLoading();
                            showToast('Error generating summary: ' + error, 'error');
                        });
                });
                
                document.getElementById('processWithLLMBtn').addEventListener('click', () => {
                    openLLMModal();
                });
                
                document.getElementById('downloadBtn').addEventListener('click', () => {
                    window.open(`/download/${paper.id}`, '_blank');
                });
            }
            
            // Open LLM modal
            function openLLMModal() {
                llmModal.style.display = 'flex';
            }
            
            // Close LLM modal
            function closeLLMModal() {
                llmModal.style.display = 'none';
                selectedLLM = null;
                llmOptions.forEach(option => {
                    option.classList.remove('selected');
                });
            }
            
            // Show loading overlay
            function showLoading(message = 'Processing...') {
                loadingText.textContent = message;
                loadingOverlay.style.display = 'flex';
            }
            
            // Hide loading overlay
            function hideLoading() {
                loadingOverlay.style.display = 'none';
            }
            
            // Show toast message
            function showToast(message, type = 'success') {
                toastMessage.textContent = message;
                toast.className = 'toast ' + type;
                toast.classList.add('show');
                
                setTimeout(() => {
                    toast.classList.remove('show');
                }, 3000);
            }
            
            // Event listeners
            paperDropdown.addEventListener('change', function() {
                const paperId = parseInt(this.value);
                if (paperId) {
                    const paper = papers.find(p => p.id === paperId);
                    if (paper) {
                        displayPaperDetails(paper);
                    }
                }
            });
            
            categoryFilter.addEventListener('change', updatePaperDropdown);
            searchBox.addEventListener('input', updatePaperDropdown);
            
            startServerBtn.addEventListener('click', function() {
                showLoading('Starting server...');
                
                fetch('/start_server', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.success) {
                            serverRunning = true;
                            showToast('Server started successfully');
                            
                            const statusDot = document.querySelector('.status-dot');
                            const statusText = document.querySelector('.status-indicator span');
                            statusDot.style.backgroundColor = 'var(--success)';
                            statusText.textContent = 'Server running';
                        } else {
                            showToast('Failed to start server: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showToast('Error starting server: ' + error, 'error');
                    });
            });
            
            refreshBtn.addEventListener('click', function() {
                loadPapers();
                showToast('Papers refreshed');
            });
            
            downloadPapersBtn.addEventListener('click', function() {
                showLoading('Downloading papers...');
                
                fetch('/download_papers', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.success) {
                            showToast(`Downloaded ${data.count} papers`);
                            loadPapers();
                        } else {
                            showToast('Failed to download papers: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showToast('Error downloading papers: ' + error, 'error');
                    });
            });
            
            reorganizePapersBtn.addEventListener('click', function() {
                showLoading('Reorganizing papers...');
                
                fetch('/reorganize_papers', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.success) {
                            showToast(`Reorganized ${data.moved} papers. ${data.failed} failed.`);
                            loadPapers();
                        } else {
                            showToast('Failed to reorganize papers: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showToast('Error reorganizing papers: ' + error, 'error');
                    });
            });
            
            llmOptions.forEach(option => {
                option.addEventListener('click', function() {
                    llmOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedLLM = this.dataset.llm;
                });
            });
            
            processBtn.addEventListener('click', function() {
                if (!selectedLLM) {
                    showToast('Please select an LLM model', 'error');
                    return;
                }
                
                if (!selectedPaper) {
                    showToast('No paper selected', 'error');
                    closeLLMModal();
                    return;
                }
                
                closeLLMModal();
                showLoading(`Processing with ${selectedLLM.toUpperCase()}...`);
                
                fetch('/process_with_llm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        paperId: selectedPaper.id,
                        llm: selectedLLM
                    })
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        // Copy to clipboard or redirect to the LLM with the content
                        if (data.url) {
                            window.open(data.url, '_blank');
                            showToast(`Opening ${selectedLLM.toUpperCase()} with paper content`);
                        } else {
                            navigator.clipboard.writeText(data.content)
                                .then(() => {
                                    showToast(`Paper content copied to clipboard for ${selectedLLM.toUpperCase()}`);
                                });
                        }
                    } else {
                        showToast('Failed to process paper: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    hideLoading();
                    showToast('Error processing paper: ' + error, 'error');
                });
            });
            
            cancelBtn.addEventListener('click', function() {
                closeLLMModal();
            });
            
            // Add event listeners for the new buttons in displayPaperDetails function
            document.getElementById('generateSummaryBtn').addEventListener('click', () => {
                showLoading('Generating summary...');
                
                fetch(`/generate_summary/${paper.id}`)
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.success) {
                            // Update the paper object with the summary
                            paper.summary = data.summary;
                            
                            // Refresh the paper details to show the summary
                            displayPaperDetails(paper);
                            showToast('Summary generated successfully');
                        } else {
                            showToast('Failed to generate summary: ' + data.error, 'error');
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        showToast('Error generating summary: ' + error, 'error');
                    });
            });

            document.getElementById('analyzeForHedgeFundBtn')?.addEventListener('click', () => {
                showLoading('Analyzing for fund strategy...');
                
                fetch(`/analyze_for_fund/${paper.id}`)
                    .then(response => response.json())
                    .then(data => {
                        hideLoading();
                        if (data.success) {
                            // Create a modal to display the analysis
                            const analysisModal = document.createElement('div');
                            analysisModal.className = 'llm-modal';
                            analysisModal.style.display = 'flex';
                            
                            const modalContent = document.createElement('div');
                            modalContent.className = 'llm-modal-content';
                            
                            // Add close button
                            const closeBtn = document.createElement('span');
                            closeBtn.className = 'close-btn';
                            closeBtn.innerHTML = '&times;';
                            closeBtn.onclick = () => {
                                analysisModal.style.display = 'none';
                                document.body.removeChild(analysisModal);
                            };
                            
                            // Add content
                            const title = document.createElement('h2');
                            title.textContent = 'Fund Strategy Analysis';
                            
                            const implementationSection = document.createElement('div');
                            implementationSection.innerHTML = `<h3>Implementation Potential</h3><p>${data.implementation_potential}</p>`;
                            
                            const strategySection = document.createElement('div');
                            strategySection.innerHTML = `<h3>Strategy Recommendations</h3><p>${data.strategy_recommendations}</p>`;
                            
                            const agentSection = document.createElement('div');
                            agentSection.innerHTML = `<h3>Agent Development</h3><p>${data.agent_development}</p>`;
                            
                            // Assemble modal
                            modalContent.appendChild(closeBtn);
                            modalContent.appendChild(title);
                            modalContent.appendChild(implementationSection);
                            modalContent.appendChild(strategySection);
                            modalContent.appendChild(agentSection);
                            
                            analysisModal.appendChild(modalContent);
                            document.body.appendChild(analysisModal);
                        } else {
                            alert(`Analysis failed: ${data.error}`);
                        }
                    })
                    .catch(error => {
                        hideLoading();
                        alert(`Error: ${error.message}`);
                        console.error('Error:', error);
                    });
            });
            
            // Initial load of papers
            loadPapers();
            
            // Check if server is running on page load
            fetch('/server_status')
                .then(response => response.json())
                .then(data => {
                    serverRunning = data.running;
                    const statusDot = document.querySelector('.status-dot');
                    const statusText = document.querySelector('.status-indicator span');
                    
                    if (serverRunning) {
                        statusDot.style.backgroundColor = 'var(--success)';
                        statusText.textContent = 'Server running';
                    } else {
                        statusDot.style.backgroundColor = 'var(--danger)';
                        statusText.textContent = 'Server not running';
                    }
                })
                .catch(() => {
                    const statusDot = document.querySelector('.status-dot');
                    const statusText = document.querySelector('.status-indicator span');
                    statusDot.style.backgroundColor = 'var(--danger)';
                    statusText.textContent = 'Server not running';
                });
        });
    </script>
</body>
</html>















