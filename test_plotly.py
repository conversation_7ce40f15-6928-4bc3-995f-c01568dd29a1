#!/usr/bin/env python3
"""
Test Plotly functionality
"""

import plotly.graph_objects as go

def test_basic_map():
    """Test basic plotly functionality"""
    print("Testing basic plotly functionality...")
    
    # Create a simple figure
    fig = go.Figure()
    
    # Add a simple scatter plot
    fig.add_trace(go.Scattergeo(
        lon=[0, 10, 20],
        lat=[0, 10, 20],
        mode='markers',
        marker=dict(size=10, color='red'),
        showlegend=False
    ))
    
    # Basic layout
    fig.update_layout(
        title="Test Map",
        geo=dict(
            projection_type='natural earth',
            showland=True,
            landcolor='lightgray'
        ),
        width=800,
        height=600
    )
    
    print("Figure created successfully")
    
    # Save as HTML
    fig.write_html('test_map.html')
    print("HTML file saved successfully")
    
    # Try PNG export
    try:
        fig.write_image('test_map.png', width=800, height=600)
        print("PNG file saved successfully")
    except Exception as e:
        print(f"PNG export failed: {e}")
    
    print("Test completed!")

if __name__ == "__main__":
    test_basic_map()
