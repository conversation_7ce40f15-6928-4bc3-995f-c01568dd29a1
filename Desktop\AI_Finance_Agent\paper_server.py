from flask import Flask, jsonify, request, send_from_directory
import os
import re
import json
import datetime
import subprocess
import threading
import time
from werkzeug.utils import secure_filename
import PyPDF2
from transformers import pipeline
import shutil

app = Flask(__name__, static_folder='static')

# Path to your papers directory
PAPERS_DIR = r"C:\Users\<USER>\OneDrive - Spectra Asset Management\Desktop\AI_Finance_Agent\papers"

# Initialize summarization pipeline
summarizer = pipeline("summarization")

# Initialize text classification pipeline for categorizing papers
classifier = pipeline("text-classification", model="facebook/bart-large-mnli")

# Server status
server_status = {
    "running": True,
    "download_process": None
}

def categorize_paper(text):
    """Determine the appropriate category for a paper based on its content."""
    # Define categories and their keywords with more specific themes
    categories = {
        "Hedge_Fund_Strategies": ["hedge fund", "long short", "market neutral", "statistical arbitrage", "stat arb", 
                                 "equity long short", "global macro", "event driven", "managed futures"],
        "Factor_Investing": ["factor investing", "smart beta", "factor model", "risk premia", "style factors", 
                            "value factor", "momentum factor", "quality factor", "low volatility"],
        "Asset_Allocation": ["asset allocation", "portfolio construction", "strategic allocation", "tactical allocation", 
                            "risk parity", "endowment model", "60-40 portfolio", "diversification"],
        "Market_Microstructure": ["market microstructure", "limit order", "market making", "liquidity", "bid-ask", 
                                 "high frequency", "order flow", "dark pools", "market impact"],
        "ML_Finance": ["machine learning", "neural network", "deep learning", "reinforcement learning", "ai", 
                      "natural language processing", "computer vision", "alternative data", "predictive modeling"],
        "Risk_Management": ["risk management", "var", "expected shortfall", "stress test", "scenario analysis", 
                           "tail risk", "drawdown", "volatility targeting", "risk budgeting"],
        "Derivatives_Structured": ["option", "derivative", "futures", "swap", "structured product", 
                                  "volatility trading", "options strategies", "convertible arbitrage"],
        "Fixed_Income": ["fixed income", "bond", "yield curve", "interest rate", "credit spread", 
                        "duration", "convexity", "term structure", "credit risk"],
        "ESG_Sustainable": ["esg", "sustainable", "climate", "green finance", "impact investing", 
                           "socially responsible", "carbon footprint", "net zero", "governance"],
        "Macro_Economics": ["macroeconomics", "monetary policy", "fiscal policy", "inflation", "gdp", 
                           "economic growth", "central bank", "business cycle", "economic indicators"]
    }
    
    # Check for category keywords in text with weighted scoring
    scores = {}
    for category, keywords in categories.items():
        score = 0
        for i, keyword in enumerate(keywords):
            weight = 1.0 if i < 5 else 0.5  # Primary keywords have higher weight
            if keyword.lower() in text.lower():
                # Count occurrences for stronger signal
                occurrences = text.lower().count(keyword.lower())
                score += weight * min(occurrences, 5)  # Cap at 5 to prevent outliers
        scores[category] = score
    
    # If no strong signal (threshold of 2.0), try using the classifier
    if max(scores.values(), default=0) < 2.0:
        try:
            # Prepare hypothesis for zero-shot classification
            hypotheses = [
                "This paper is about hedge fund strategies and investment approaches.",
                "This paper is about factor investing and smart beta strategies.",
                "This paper is about asset allocation and portfolio construction.",
                "This paper is about market microstructure and trading mechanisms.",
                "This paper is about machine learning applications in finance.",
                "This paper is about risk management techniques and frameworks.",
                "This paper is about derivatives and structured products.",
                "This paper is about fixed income securities and interest rates.",
                "This paper is about ESG and sustainable investing approaches.",
                "This paper is about macroeconomic analysis and policy impacts."
            ]
            
            # Use a larger sample for better classification
            sample_text = text[:2000]
            
            # Get classification results
            results = classifier(sample_text, hypotheses, multi_label=True)
            
            # Map result to category
            result_mapping = {
                0: "Hedge_Fund_Strategies",
                1: "Factor_Investing",
                2: "Asset_Allocation",
                3: "Market_Microstructure",
                4: "ML_Finance",
                5: "Risk_Management",
                6: "Derivatives_Structured",
                7: "Fixed_Income",
                8: "ESG_Sustainable",
                9: "Macro_Economics"
            }
            
            # Get highest scoring category
            highest_score = 0
            best_idx = 0
            for result in results:
                if result['score'] > highest_score:
                    highest_score = result['score']
                    best_idx = int(result['label'].split('_')[-1])
            
            return result_mapping[best_idx]
        except Exception as e:
            print(f"Classification error: {e}")
            return "Uncategorized"
    
    # Return category with highest score
    max_category = max(scores.items(), key=lambda x: x[1])[0]
    return max_category if scores[max_category] > 0 else "Uncategorized"

@app.route('/')
def index():
    return send_from_directory('.', 'paper_library.html')

@app.route('/server_status')
def get_server_status():
    return jsonify({
        "running": server_status["running"],
        "download_in_progress": server_status["download_process"] is not None
    })

@app.route('/start_server', methods=['POST'])
def start_server():
    # This endpoint is mostly symbolic since the server is already running
    # when this endpoint is called, but it could be used to restart services
    try:
        server_status["running"] = True
        return jsonify({
            "success": True,
            "message": "Server started successfully"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/download_papers', methods=['POST'])
def download_papers():
    if server_status["download_process"] is not None:
        if server_status["download_process"].is_alive():
            return jsonify({
                "success": False,
                "error": "Download already in progress"
            })
    
    try:
        # Start the download_papers.py script in a separate thread
        def run_download():
            subprocess.run(["python", "download_papers.py"])
        
        server_status["download_process"] = threading.Thread(target=run_download)
        server_status["download_process"].start()
        
        return jsonify({
            "success": True,
            "message": "Download process started"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

@app.route('/get_papers')
def get_papers():
    papers = []
    paper_id = 0
    categories = set()
    
    # Walk through all subdirectories
    for root, dirs, files in os.walk(PAPERS_DIR):
        for file in files:
            if file.endswith('.pdf'):
                # Get relative path for category
                rel_path = os.path.relpath(root, PAPERS_DIR)
                category = rel_path if rel_path != '.' else 'Uncategorized'
                categories.add(category)
                
                # Clean up the title (remove .pdf and replace underscores with spaces)
                title = os.path.splitext(file)[0].replace('_', ' ')
                
                # Get file creation/modification time
                file_path = os.path.join(root, file)
                timestamp = os.path.getmtime(file_path)
                date = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                
                papers.append({
                    'id': paper_id,
                    'title': title,
                    'path': f'/view_pdf/{paper_id}',
                    'category': category,
                    'published': date,
                    'summary': None,
                    'file_path': file_path
                })
                paper_id += 1
    
    # Sort papers by date (newest first)
    papers.sort(key=lambda x: x['published'], reverse=True)
    
    return jsonify(papers)

@app.route('/get_categories')
def get_categories():
    categories = set()
    
    # Walk through all subdirectories
    for root, dirs, files in os.walk(PAPERS_DIR):
        for file in files:
            if file.endswith('.pdf'):
                # Get relative path for category
                rel_path = os.path.relpath(root, PAPERS_DIR)
                category = rel_path if rel_path != '.' else 'Uncategorized'
                categories.add(category)
    
    return jsonify(list(categories))

@app.route('/search_papers')
def search_papers():
    search_term = request.args.get('term', '').lower()
    category_filter = request.args.get('category', 'all')
    
    # Get all papers first
    response = app.test_client().get('/get_papers')
    all_papers = json.loads(response.data)
    
    # Filter papers based on search term and category
    filtered_papers = []
    for paper in all_papers:
        title_match = search_term in paper['title'].lower()
        category_match = (category_filter == 'all' or 
                          (category_filter in paper['category']))
        
        if title_match and category_match:
            filtered_papers.append(paper)
    
    return jsonify(filtered_papers)

@app.route('/view_pdf/<int:paper_id>')
def view_pdf(paper_id):
    # Get paper details
    response = app.test_client().get('/get_papers')
    all_papers = json.loads(response.data)
    
    for paper in all_papers:
        if paper['id'] == paper_id:
            file_path = paper['file_path']
            directory, filename = os.path.split(file_path)
            return send_from_directory(directory, filename)
    
    return "PDF not found", 404

@app.route('/generate_summary/<int:paper_id>')
def generate_summary(paper_id):
    # Get paper details
    response = app.test_client().get('/get_papers')
    all_papers = json.loads(response.data)
    
    for paper in all_papers:
        if paper['id'] == paper_id:
            try:
                # Extract text from PDF
                text = extract_text_from_pdf(paper['file_path'])
                
                # Generate summary (first 1000 chars to avoid token limits)
                summary_text = summarize_text(text[:5000])
                
                return jsonify({
                    'success': True,
                    'summary': summary_text
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                })
    
    return jsonify({
        'success': False,
        'error': 'Paper not found'
    })

@app.route('/process_with_llm/<int:paper_id>')
def process_with_llm(paper_id):
    llm_type = request.args.get('llm', 'claude')
    
    # Get paper details
    response = app.test_client().get('/get_papers')
    all_papers = json.loads(response.data)
    
    for paper in all_papers:
        if paper['id'] == paper_id:
            try:
                # Extract text from PDF
                text = extract_text_from_pdf(paper['file_path'])
                
                # Truncate text to avoid overwhelming the LLM
                truncated_text = text[:10000] + "..." if len(text) > 10000 else text
                
                # Format prompt
                prompt = f"Please analyze this research paper and provide key insights:\n\nTitle: {paper['title']}\n\nContent: {truncated_text}"
                
                # Different handling based on LLM type
                if llm_type == 'claude':
                    # For Claude, we'll just copy to clipboard
                    return jsonify({
                        'success': True,
                        'content': prompt
                    })
                elif llm_type == 'gpt':
                    # For ChatGPT, we could generate a URL with the prompt
                    return jsonify({
                        'success': True,
                        'content': prompt,
                        'url': f"https://chat.openai.com/?prompt={prompt[:100]}..."
                    })
                elif llm_type == 'grok':
                    # For Grok, similar to Claude
                    return jsonify({
                        'success': True,
                        'content': prompt
                    })
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                })
    
    return jsonify({
        'success': False,
        'error': 'Paper not found'
    })

@app.route('/reorganize_papers', methods=['POST'])
def reorganize_papers():
    """Reorganize papers in the root directory into category folders based on content."""
    try:
        # Get all PDF files in the root directory
        root_files = [f for f in os.listdir(PAPERS_DIR) 
                     if os.path.isfile(os.path.join(PAPERS_DIR, f)) and f.endswith('.pdf')]
        
        moved_count = 0
        failed_count = 0
        
        for filename in root_files:
            try:
                file_path = os.path.join(PAPERS_DIR, filename)
                
                # Extract text from PDF
                text = extract_text_from_pdf(file_path)
                
                # Determine category
                category = categorize_paper(text)
                
                # Create category directory if it doesn't exist
                category_dir = os.path.join(PAPERS_DIR, category)
                os.makedirs(category_dir, exist_ok=True)
                
                # Move file to category directory
                dest_path = os.path.join(category_dir, filename)
                shutil.move(file_path, dest_path)
                moved_count += 1
                print(f"Moved {filename} to {category}")
                
            except Exception as e:
                print(f"Error processing {filename}: {e}")
                failed_count += 1
        
        return jsonify({
            "success": True,
            "message": f"Reorganized {moved_count} papers. {failed_count} failed.",
            "moved": moved_count,
            "failed": failed_count
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        })

def extract_text_from_pdf(file_path):
    """Extract text from a PDF file."""
    try:
        with open(file_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            text = ""
            # Limit to first 10 pages to avoid processing very large documents
            max_pages = min(10, len(reader.pages))
            for i in range(max_pages):
                text += reader.pages[i].extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Error extracting text from {file_path}: {e}")
        return ""

def summarize_text(text):
    """Generate a comprehensive summary of at least 500 words."""
    try:
        # For longer summaries, we'll use a chunking approach
        chunks = [text[i:i+1000] for i in range(0, min(len(text), 10000), 1000)]
        summaries = []
        
        for chunk in chunks:
            summary = summarizer(chunk, max_length=200, min_length=100, do_sample=False)
            summaries.append(summary[0]['summary_text'])
        
        # Combine summaries into one comprehensive summary
        full_summary = " ".join(summaries)
        
        # If still too short, add more context from the beginning of the paper
        if len(full_summary.split()) < 500 and len(text) > 5000:
            # Extract key sections that often contain important information
            intro_text = text[:3000]  # Introduction often has key points
            conclusion_text = text[-3000:] if len(text) > 6000 else ""  # Conclusion
            
            intro_summary = summarizer(intro_text, max_length=200, min_length=100, do_sample=False)[0]['summary_text']
            conclusion_summary = ""
            if conclusion_text:
                conclusion_summary = summarizer(conclusion_text, max_length=200, min_length=100, do_sample=False)[0]['summary_text']
            
            full_summary = f"Introduction: {intro_summary}\n\nMain Content: {full_summary}\n\nConclusion: {conclusion_summary}"
        
        return full_summary
    except Exception as e:
        print(f"Error in summarization: {e}")
        # Fallback to a simple extraction-based summary
        sentences = text.split('.')
        important_sentences = sentences[:50]  # Take first 50 sentences to ensure length
        return '. '.join(important_sentences) + '.'

@app.route('/analyze_for_fund/<int:paper_id>')
def analyze_for_fund(paper_id):
    # Get paper details
    try:
        response = app.test_client().get('/get_papers')
        all_papers = json.loads(response.data)
        
        paper_found = False
        for paper in all_papers:
            if paper['id'] == paper_id:
                paper_found = True
                try:
                    # Extract text from PDF
                    text = extract_text_from_pdf(paper['file_path'])
                    
                    # Truncate text to avoid token limits
                    truncated_text = text[:7000] + "..." if len(text) > 7000 else text
                    
                    # Analyze for hedge fund implementation
                    implementation_potential = analyze_implementation_potential(text)
                    strategy_recommendations = generate_strategy_recommendations(text, paper['title'])
                    agent_development = suggest_agent_development(text)
                    
                    print(f"Analysis complete for paper ID {paper_id}")
                    print(f"Implementation potential: {implementation_potential['score']}")
                    
                    return jsonify({
                        'success': True,
                        'implementation_potential': implementation_potential['description'],
                        'strategy_recommendations': strategy_recommendations,
                        'agent_development': agent_development
                    })
                except Exception as e:
                    print(f"Error analyzing paper: {e}")
                    import traceback
                    traceback.print_exc()
                    return jsonify({
                        'success': False,
                        'error': str(e)
                    })
        
        if not paper_found:
            return jsonify({
                'success': False,
                'error': f'Paper with ID {paper_id} not found'
            })
            
    except Exception as e:
        print(f"Error in analyze_for_fund: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

def analyze_implementation_potential(text):
    """Analyze the potential for implementing the paper in a hedge fund strategy."""
    implementation_keywords = [
        "backtest", "empirical results", "performance", "returns", "sharpe ratio",
        "implementation", "trading strategy", "algorithm", "signal generation",
        "portfolio construction", "risk management", "execution", "transaction costs",
        "market impact", "liquidity", "practical considerations", "real-world",
        "out-of-sample", "robustness", "statistical significance"
    ]
    
    implementation_score = sum(1 for keyword in implementation_keywords if keyword.lower() in text.lower())
    
    # Check for specific metrics that indicate implementability
    metrics_present = any(metric in text.lower() for metric in 
                         ["sharpe ratio", "information ratio", "sortino ratio", "calmar ratio", 
                          "maximum drawdown", "win rate", "profit factor"])
    
    # Check for backtesting details
    backtest_details = any(detail in text.lower() for detail in 
                          ["out-of-sample", "walk-forward", "cross-validation", 
                           "transaction costs", "slippage", "market impact"])
    
    # Adjust score based on presence of metrics and backtest details
    if metrics_present:
        implementation_score += 3
    if backtest_details:
        implementation_score += 3
    
    if implementation_score >= 10:
        return {
            "score": "High",
            "description": "High potential for implementation. The paper presents comprehensive empirical results and backtested strategies that could be directly applied to trading systems.",
            "next_steps": "Consider immediate prototype development and paper trading to validate the strategy in current market conditions."
        }
    elif implementation_score >= 5:
        return {
            "score": "Medium",
            "description": "Moderate potential for implementation. The paper contains practical insights but may require additional research and development.",
            "next_steps": "Conduct further analysis to fill gaps in the methodology and perform additional backtesting with current market data."
        }
    else:
        return {
            "score": "Low",
            "description": "Low potential for implementation. The paper appears to be primarily theoretical with limited direct application to trading strategies.",
            "next_steps": "Consider extracting conceptual insights that could inform broader research rather than direct implementation."
        }

def generate_strategy_recommendations(text, title):
    """Generate recommendations for incorporating the paper into trading strategies."""
    # Check for specific strategy types
    strategy_types = {
        "statistical arbitrage": "The paper's statistical arbitrage approach could be implemented as a market-neutral strategy, focusing on pairs trading or relative value opportunities.",
        "momentum": "Consider implementing the momentum factors described in the paper as part of a multi-factor model, potentially combining with value signals for diversification.",
        "machine learning": "The machine learning models described could be used to enhance alpha signal generation, particularly for short-term forecasting and pattern recognition.",
        "deep learning": "The deep learning architecture could be adapted for high-frequency trading signals or alternative data processing.",
        "reinforcement learning": "The reinforcement learning approach could be valuable for optimizing execution strategies or dynamic portfolio allocation.",
        "nlp": "The natural language processing techniques could be applied to news sentiment analysis or earnings call transcripts for alpha generation."
    }
    
    recommendations = []
    
    for strategy, recommendation in strategy_types.items():
        if strategy.lower() in text.lower() or strategy.lower() in title.lower():
            recommendations.append(recommendation)
    
    if not recommendations:
        recommendations.append("Consider using the paper's methodologies as part of a research-driven systematic strategy, focusing on the specific markets or asset classes mentioned.")
        recommendations.append("The techniques might be most suitable for integration into an existing quantitative framework rather than as a standalone strategy.")
    
    return " ".join(recommendations)

def suggest_agent_development(text):
    """Suggest how to develop an AI agent based on the paper."""
    agent_frameworks = []
    
    # Check for specific technologies
    if any(term in text.lower() for term in ["reinforcement learning", "rl", "q-learning", "dqn"]):
        agent_frameworks.append("Implement a reinforcement learning agent using frameworks like Stable Baselines3 or RLlib, focusing on the reward structure described in the paper.")
    
    if any(term in text.lower() for term in ["transformer", "bert", "gpt", "llm"]):
        agent_frameworks.append("Develop a transformer-based language model agent using Hugging Face Transformers or similar libraries to process textual financial data as described in the paper.")
    
    if any(term in text.lower() for term in ["lstm", "rnn", "gru", "recurrent"]):
        agent_frameworks.append("Build a recurrent neural network agent using PyTorch or TensorFlow to capture the temporal patterns identified in the research.")
    
    if any(term in text.lower() for term in ["cnn", "convolutional"]):
        agent_frameworks.append("Create a convolutional neural network agent to process the structured data patterns described in the paper.")
    
    if any(term in text.lower() for term in ["ensemble", "boosting", "random forest"]):
        agent_frameworks.append("Implement an ensemble learning agent using libraries like XGBoost or LightGBM to combine multiple models as suggested in the research.")
    
    if not agent_frameworks:
        agent_frameworks.append("Consider developing a hybrid agent that combines traditional financial models with machine learning components to implement the paper's methodology.")
        agent_frameworks.append("Focus on creating an agent with strong backtesting capabilities to validate the paper's claims before live implementation.")
    
    return " ".join(agent_frameworks)

if __name__ == '__main__':
    # Make sure the required packages are installed
    try:
        import flask
        import PyPDF2
        import transformers
    except ImportError:
        import subprocess
        print("Installing required packages...")
        subprocess.check_call(["pip", "install", "flask", "PyPDF2", "transformers"])
    
    # Create static folder if it doesn't exist
    os.makedirs('static', exist_ok=True)
    
    # Start the server
    app.run(debug=True, port=5000)














