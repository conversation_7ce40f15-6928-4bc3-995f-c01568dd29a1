#!/usr/bin/env python3
"""
CFR-Style Strategic Maritime Map - Simplified Version
Creates a minimalist strategic maritime map following Council on Foreign Relations visualization standards
"""

import plotly.graph_objects as go

def create_cfr_maritime_map():
    """Create CFR-style strategic maritime map with chokepoints and shipping routes"""
    
    # Key chokepoints coordinates
    chokepoints = {
        'Suez Canal': {'lat': 30.0444, 'lon': 32.3667},
        'Strait of Hormuz': {'lat': 26.5667, 'lon': 56.2500},
        'Bab el-Mandeb': {'lat': 12.5833, 'lon': 43.3333}
    }
    
    # Traditional route coordinates (Saudi Arabia through chokepoints)
    traditional_route_lons = [46.6753, 56.2500, 43.3333, 32.3667, 25.0, 15.0]
    traditional_route_lats = [24.7136, 26.5667, 12.5833, 30.0444, 35.0, 40.0]
    
    # NEOM Corridor route (Serbia → Danube → Port of Bar → around Africa)
    neom_route_lons = [21.0059, 20.4633, 19.0864, 15.0, 10.0, 0.0, -10.0, -15.0, 10.0, 20.0, 40.0, 43.3333, 45.0, 46.6753]
    neom_route_lats = [44.0165, 44.8176, 42.0941, 40.0, 35.0, 30.0, 20.0, 0.0, -20.0, -35.0, -10.0, 12.5833, 20.0, 24.7136]
    
    # Create the figure
    fig = go.Figure()
    
    # Add chokepoints as red X markers
    for point_name, coords in chokepoints.items():
        fig.add_trace(go.Scattergeo(
            lon=[coords['lon']],
            lat=[coords['lat']],
            mode='markers+text',
            marker=dict(
                symbol='x',
                size=12,
                color='red',
                line=dict(width=2, color='red')
            ),
            text=[point_name],
            textposition='top center',
            textfont=dict(family='Arial', size=9, color='black'),
            showlegend=False,
            name=point_name
        ))
    
    # Add traditional route (dashed black line)
    fig.add_trace(go.Scattergeo(
        lon=traditional_route_lons,
        lat=traditional_route_lats,
        mode='lines',
        line=dict(width=2, color='black', dash='dash'),
        name='Traditional Route: 45-50 days',
        showlegend=True
    ))
    
    # Add NEOM Corridor route (solid blue line)
    fig.add_trace(go.Scattergeo(
        lon=neom_route_lons,
        lat=neom_route_lats,
        mode='lines',
        line=dict(width=2, color='blue', dash='solid'),
        name='NEOM Corridor Route: 27-32 days',
        showlegend=True
    ))
    
    # Add key country labels
    fig.add_trace(go.Scattergeo(
        lon=[46.6753, 21.0059],
        lat=[24.7136, 44.0165],
        mode='text',
        text=['Saudi Arabia', 'Serbia'],
        textfont=dict(family='Arial', size=9, color='black'),
        showlegend=False
    ))
    
    # Configure layout with CFR styling
    fig.update_layout(
        title=dict(
            text='Strategic Maritime Routes: Traditional vs NEOM Corridor',
            font=dict(family='Arial', size=14, color='black'),
            x=0.5
        ),
        geo=dict(
            projection_type='natural earth',
            showland=True,
            landcolor='#E5E5E5',  # Light gray landmasses
            showocean=True,
            oceancolor='white',   # White water
            showlakes=True,
            lakecolor='white',
            showcountries=False,  # No country borders
            showcoastlines=True,
            coastlinecolor='#CCCCCC',
            coastlinewidth=0.5,
            showframe=False,
            lonaxis=dict(range=[-15, 65]),
            lataxis=dict(range=[5, 50]),
            bgcolor='white'
        ),
        showlegend=True,
        legend=dict(
            x=0.02, y=0.98,
            bgcolor='rgba(255,255,255,0.8)',
            bordercolor='black',
            borderwidth=1,
            font=dict(family='Arial', size=9)
        ),
        width=1200,
        height=800,
        margin=dict(l=0, r=0, t=50, b=0),
        paper_bgcolor='white'
    )
    
    return fig

def main():
    """Main function to create and export the CFR-style maritime map"""
    print("Creating CFR-style Strategic Maritime Map...")
    
    # Create the map
    fig = create_cfr_maritime_map()
    
    # Save as HTML (always works)
    fig.write_html('cfr_strategic_maritime_map.html')
    print("Interactive map saved as cfr_strategic_maritime_map.html")
    
    # Try to save as PNG
    try:
        fig.write_image('cfr_strategic_maritime_map.png', width=1200, height=800, scale=2.5)
        print("High-resolution PNG saved as cfr_strategic_maritime_map.png")
    except Exception as e:
        print(f"PNG export failed: {e}")
    
    # Note: fig.show() removed to prevent hanging in some environments
    
    print("\nMap Features:")
    print("✓ White background with light gray (#E5E5E5) landmasses")
    print("✓ Three chokepoints marked with red X symbols")
    print("✓ Traditional route: black dashed line (45-50 days)")
    print("✓ NEOM Corridor route: blue solid line (27-32 days)")
    print("✓ Minimal labels in Arial 9pt font")
    print("✓ CFR-style professional appearance")

if __name__ == "__main__":
    main()
