from sentence_transformers import SentenceTransformer
import faiss
import numpy as np
import pickle

model = SentenceTransformer('all-MiniLM-L6-v2')
with open("paper_texts.txt", "r", encoding="utf-8") as f:
    paper_texts = f.read().split("---\n")

embeddings = model.encode(paper_texts, show_progress_bar=True)
index = faiss.IndexFlatIP(384)
index.add(np.array(embeddings))

faiss.write_index(index, "paper_index.faiss")
with open("paper_texts.pkl", "wb") as f:
    pickle.dump(paper_texts, f)