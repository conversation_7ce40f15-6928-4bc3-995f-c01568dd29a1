<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CFR Strategic Maritime Map - NEOM Corridor</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .map-container {
            width: 100%;
            height: 800px;
            border: 1px solid #ccc;
        }
        .title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: black;
        }
        .export-buttons {
            text-align: center;
            margin-top: 20px;
        }
        .export-buttons button {
            margin: 0 10px;
            padding: 10px 20px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            cursor: pointer;
            font-family: Arial, sans-serif;
        }
        .export-buttons button:hover {
            background-color: #e0e0e0;
        }
        .features {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        .features h3 {
            margin-top: 0;
            color: #333;
        }
        .features ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .features li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="title">Strategic Maritime Routes: Traditional vs NEOM Corridor</div>
    <div id="map" class="map-container"></div>
    <div class="export-buttons">
        <button onclick="exportPNG()">Export as PNG (300 DPI)</button>
        <button onclick="exportHTML()">Save HTML</button>
    </div>
    
    <div class="features">
        <h3>CFR-Style Map Features:</h3>
        <ul>
            <li>✓ White background with light gray (#E5E5E5) landmasses</li>
            <li>✓ Three strategic chokepoints marked with red X symbols</li>
            <li>✓ Three KSA west coast ports marked with blue circles (NEOM, Yanbu, Jeddah)</li>
            <li>✓ Traditional route: black dashed line (45-50 days transit time)</li>
            <li>✓ NEOM Corridor maritime route: blue solid line (KSA west coast → Suez → Bar port)</li>
            <li>✓ NEOM Corridor railway: green solid line (Bar port → Belgrade, Serbia)</li>
            <li>✓ Minimal labels in Arial 9pt font</li>
            <li>✓ Professional CFR/Foreign Affairs magazine styling</li>
            <li>✓ High-resolution export capability (1200x800px, 300 DPI equivalent)</li>
        </ul>
    </div>

    <script>
        // Key chokepoints coordinates
        const chokepoints = {
            'Suez Canal': {lat: 30.0444, lon: 32.3667},
            'Strait of Hormuz': {lat: 26.5667, lon: 56.2500},
            'Bab el-Mandeb': {lat: 12.5833, lon: 43.3333}
        };

        // Traditional route coordinates (Saudi Arabia through chokepoints)
        const traditionalRoute = {
            lons: [46.6753, 56.2500, 43.3333, 32.3667, 25.0, 15.0],
            lats: [24.7136, 26.5667, 12.5833, 30.0444, 35.0, 40.0]
        };

        // NEOM Corridor route (KSA west coast ports → Suez Canal → Mediterranean → Bar port → railway to Belgrade)
        const neomRoute = {
            // Maritime portion: KSA west coast → Suez Canal → Mediterranean → Bar port
            maritime: {
                lons: [39.1637, 38.9637, 38.7637, 39.2000, 32.3667, 25.0, 18.0, 19.0864],  // 3 KSA west coast ports → Suez → Mediterranean → Bar
                lats: [21.4858, 22.3858, 23.2858, 25.0000, 30.0444, 35.0, 40.0, 42.0941]   // NEOM, Yanbu, Jeddah areas → Suez → Med → Bar
            },
            // Railway portion: Bar port → Belgrade
            railway: {
                lons: [19.0864, 19.5, 20.0, 20.4633],  // Bar → inland → Belgrade
                lats: [42.0941, 43.0, 43.5, 44.8176]   // Bar → railway route → Belgrade
            }
        };

        // Create traces
        const traces = [];

        // Add chokepoints as red X markers
        Object.entries(chokepoints).forEach(([name, coords]) => {
            traces.push({
                type: 'scattergeo',
                lon: [coords.lon],
                lat: [coords.lat],
                mode: 'markers+text',
                marker: {
                    symbol: 'x',
                    size: 15,
                    color: 'red',
                    line: {width: 3, color: 'red'}
                },
                text: [name],
                textposition: 'top center',
                textfont: {
                    family: 'Arial',
                    size: 9,
                    color: 'black'
                },
                showlegend: false,
                name: name,
                hovertemplate: name + '<extra></extra>'
            });
        });

        // Add KSA west coast ports as blue circles
        const ksaPorts = [
            {name: 'NEOM', lat: 21.4858, lon: 39.1637},
            {name: 'Yanbu', lat: 22.3858, lon: 38.9637},
            {name: 'Jeddah', lat: 23.2858, lon: 38.7637}
        ];
        
        ksaPorts.forEach(port => {
            traces.push({
                type: 'scattergeo',
                lon: [port.lon],
                lat: [port.lat],
                mode: 'markers+text',
                marker: {
                    symbol: 'circle',
                    size: 8,
                    color: 'blue',
                    line: {width: 2, color: 'darkblue'}
                },
                text: [port.name],
                textposition: 'bottom center',
                textfont: {
                    family: 'Arial',
                    size: 8,
                    color: 'black'
                },
                showlegend: false,
                name: port.name,
                hovertemplate: port.name + ' Port<extra></extra>'
            });
        });

        // Add traditional route (dashed black line)
        traces.push({
            type: 'scattergeo',
            lon: traditionalRoute.lons,
            lat: traditionalRoute.lats,
            mode: 'lines',
            line: {
                width: 2,
                color: 'black',
                dash: 'dash'
            },
            name: 'Traditional Route: 45-50 days',
            showlegend: true,
            hovertemplate: 'Traditional Route: 45-50 days<extra></extra>'
        });

        // Add NEOM Corridor maritime route (solid blue line)
        traces.push({
            type: 'scattergeo',
            lon: neomRoute.maritime.lons,
            lat: neomRoute.maritime.lats,
            mode: 'lines',
            line: {
                width: 2,
                color: 'blue',
                dash: 'solid'
            },
            name: 'NEOM Corridor (Maritime): 27-32 days',
            showlegend: true,
            hovertemplate: 'NEOM Corridor Maritime Route: KSA West Coast → Suez → Mediterranean → Bar Port<extra></extra>'
        });

        // Add NEOM Corridor railway route (solid green line)
        traces.push({
            type: 'scattergeo',
            lon: neomRoute.railway.lons,
            lat: neomRoute.railway.lats,
            mode: 'lines',
            line: {
                width: 3,
                color: 'green',
                dash: 'solid'
            },
            name: 'NEOM Corridor (Railway): Bar → Belgrade',
            showlegend: true,
            hovertemplate: 'NEOM Corridor Railway: Bar Port → Belgrade<extra></extra>'
        });

        // Add key location labels
        traces.push({
            type: 'scattergeo',
            lon: [39.5, 20.4633, 19.0864],
            lat: [22.5, 44.8176, 42.0941],
            mode: 'text',
            text: ['KSA West Coast Ports', 'Belgrade', 'Bar Port'],
            textfont: {
                family: 'Arial',
                size: 9,
                color: 'black'
            },
            showlegend: false,
            hoverinfo: 'skip'
        });

        // Layout configuration with CFR styling
        const layout = {
            geo: {
                projection: {type: 'natural earth'},
                showland: true,
                landcolor: '#E5E5E5',  // Light gray landmasses
                showocean: true,
                oceancolor: 'white',   // White water
                showlakes: true,
                lakecolor: 'white',
                showcountries: false,  // No country borders
                showcoastlines: true,
                coastlinecolor: '#CCCCCC',
                coastlinewidth: 0.5,
                showframe: false,
                lonaxis: {range: [-15, 65]},
                lataxis: {range: [5, 50]},
                bgcolor: 'white'
            },
            showlegend: true,
            legend: {
                x: 0.02,
                y: 0.98,
                bgcolor: 'rgba(255,255,255,0.8)',
                bordercolor: 'black',
                borderwidth: 1,
                font: {family: 'Arial', size: 9}
            },
            width: 1200,
            height: 800,
            margin: {l: 0, r: 0, t: 0, b: 0},
            paper_bgcolor: 'white',
            plot_bgcolor: 'white'
        };

        // Configuration for the plot
        const config = {
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'select2d', 'lasso2d', 'autoScale2d'],
            displaylogo: false,
            toImageButtonOptions: {
                format: 'png',
                filename: 'cfr_strategic_maritime_map',
                height: 800,
                width: 1200,
                scale: 2.5  // For high resolution (300 DPI equivalent)
            }
        };

        // Create the plot
        Plotly.newPlot('map', traces, layout, config);

        // Export functions
        function exportPNG() {
            Plotly.toImage('map', {
                format: 'png',
                width: 1200,
                height: 800,
                scale: 2.5
            }).then(function(dataURL) {
                const link = document.createElement('a');
                link.download = 'cfr_strategic_maritime_map.png';
                link.href = dataURL;
                link.click();
            });
        }

        function exportHTML() {
            const htmlContent = document.documentElement.outerHTML;
            const blob = new Blob([htmlContent], {type: 'text/html'});
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = 'cfr_strategic_maritime_map.html';
            link.href = url;
            link.click();
            URL.revokeObjectURL(url);
        }

        console.log('CFR Strategic Maritime Map loaded successfully');
    </script>
</body>
</html>
