import arxiv
import requests
import os
import datetime
import re
from datetime import <PERSON><PERSON><PERSON>

def download_papers(query, max_results=300, date_range=7):
    today = datetime.datetime.now().date()
    
    print(f"Looking for papers published in the last {date_range} days")
    
    search = arxiv.Search(
        query=query,
        max_results=max_results,
        sort_by=arxiv.SortCriterion.SubmittedDate,
        sort_order=arxiv.SortOrder.Descending
    )
    
    papers = []
    for result in search.results():
        published_date = result.published.date()
        
        if (today - published_date).days <= date_range:
            paper_id = result.entry_id.split('/')[-1]
            papers.append({
                'id': paper_id,
                'title': result.title,
                'pdf_url': result.pdf_url,
                'published': published_date,
                'categories': result.categories,
                'primary_category': result.primary_category
            })
    
    return papers

def sanitize_filename(title):
    sanitized = re.sub(r'[\\/*?:"<>|]', "", title)
    sanitized = sanitized.replace(' ', '_')
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    return sanitized

def download_pdf(paper, base_dir="papers", downloaded_files=None):
    if downloaded_files is None:
        downloaded_files = set()
        
    # Get primary category for folder organization
    category = paper['primary_category']
    category_dir = os.path.join(base_dir, category)
    os.makedirs(category_dir, exist_ok=True)
    
    sanitized_title = sanitize_filename(paper['title'])
    filename = os.path.join(category_dir, f"{sanitized_title}.pdf")
    
    # Check if we've already downloaded this paper in this session
    paper_id = paper['id']
    if paper_id in downloaded_files:
        print(f"Already processed in this session: {paper['title']}")
        return
    
    # Check if the file already exists on disk
    if os.path.exists(filename):
        print(f"Already exists on disk: {paper['title']}")
        downloaded_files.add(paper_id)
        return
    
    try:
        response = requests.get(paper['pdf_url'], timeout=30)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            f.write(response.content)
        
        print(f"Downloaded: {paper['title']} to {category_dir}")
        downloaded_files.add(paper_id)
    except Exception as e:
        print(f"Error downloading {paper['title']}: {e}")
    
    return downloaded_files

# Create base papers directory
os.makedirs("papers", exist_ok=True)

# Define categories with correct formatting
categories = [
    "cs.AI",  # Artificial Intelligence
    "cs.CE",  # Computational Engineering
    "cs.GT",  # Game Theory
    "cs.DS",  # Data Structures
    "cs.SI",  # Social and Information Networks
    "q-fin.*"  # All finance subcategories
]

# Dictionary to track papers by ID to avoid duplicates
all_papers = {}
downloaded_files = set()

# For each category, use a date-sorted search to get recent papers
for category in categories:
    print(f"Searching for papers in {category}...")
    query = f"cat:{category}"
    category_papers = download_papers(query, date_range=14)
    print(f"Found {len(category_papers)} recent papers in {category}")
    
    for paper in category_papers:
        all_papers[paper['id']] = paper

# If no papers found, try with a broader search
if len(all_papers) == 0:
    print("No papers found with category search. Trying broader search...")
    for category in categories:
        query = f"all:{category}"
        category_papers = download_papers(query, date_range=30)
        for paper in category_papers:
            all_papers[paper['id']] = paper

print(f"Found {len(all_papers)} unique papers across all categories")

# Download all unique papers
for paper in all_papers.values():
    downloaded_files = download_pdf(paper, downloaded_files=downloaded_files)

print("Download completed!")
