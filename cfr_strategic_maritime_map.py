#!/usr/bin/env python3
"""
CFR-Style Strategic Maritime Map
Creates a minimalist strategic maritime map following Council on Foreign Relations visualization standards
Focus: Mediterranean, Red Sea, Arabian Peninsula, and Balkans with shipping route analysis
"""

import plotly.graph_objects as go
import plotly.io as pio
import numpy as np

def create_cfr_maritime_map():
    """Create CFR-style strategic maritime map with chokepoints and shipping routes"""
    
    # Define geographic bounds for the region of interest
    # Focus on Mediterranean, Red Sea, Arabian Peninsula, and Balkans
    lat_range = [10, 50]  # From Red Sea to Balkans
    lon_range = [-10, 60]  # From Atlantic to Arabian Peninsula
    
    # Key chokepoints coordinates
    chokepoints = {
        'Suez Canal': {'lat': 30.0444, 'lon': 32.3667, 'name': 'Suez Canal'},
        'Strait of Hormuz': {'lat': 26.5667, 'lon': 56.2500, 'name': 'Strait of Hormuz'},
        'Bab el-Mandeb': {'lat': 12.5833, 'lon': 43.3333, 'name': 'Bab el-Mandeb'}
    }
    
    # Key locations for routes
    locations = {
        'Saudi Arabia': {'lat': 24.7136, 'lon': 46.6753},
        'Serbia': {'lat': 44.0165, 'lon': 21.0059},
        'Port of Bar': {'lat': 42.0941, 'lon': 19.0864},
        'Danube': {'lat': 44.8176, 'lon': 20.4633}  # Belgrade area
    }
    
    # Traditional route coordinates (Saudi Arabia through chokepoints)
    traditional_route = [
        [24.7136, 46.6753],  # Saudi Arabia
        [26.5667, 56.2500],  # Strait of Hormuz
        [12.5833, 43.3333],  # Bab el-Mandeb
        [30.0444, 32.3667],  # Suez Canal
        [35.0, 25.0],        # Eastern Mediterranean
        [40.0, 15.0]         # Central Mediterranean
    ]
    
    # NEOM Corridor route (Serbia → Danube → Port of Bar → around Africa)
    neom_route = [
        [44.0165, 21.0059],  # Serbia
        [44.8176, 20.4633],  # Danube (Belgrade)
        [42.0941, 19.0864],  # Port of Bar, Montenegro
        [40.0, 15.0],        # Adriatic Sea
        [35.0, 10.0],        # Central Mediterranean
        [30.0, 0.0],         # Western Mediterranean
        [20.0, -10.0],       # Atlantic (Gibraltar area)
        [0.0, -15.0],        # West Africa
        [-20.0, 10.0],       # South Atlantic
        [-35.0, 20.0],       # Cape of Good Hope
        [-10.0, 40.0],       # Indian Ocean
        [12.5833, 43.3333],  # Bab el-Mandeb
        [20.0, 45.0],        # Red Sea
        [24.7136, 46.6753]   # Saudi Arabia
    ]
    
    # Create the figure
    fig = go.Figure()
    
    # Add base map with CFR styling
    fig.add_trace(go.Scattergeo(
        lon=[0], lat=[0],  # Dummy point to initialize
        mode='markers',
        marker=dict(size=0, opacity=0),
        showlegend=False,
        hoverinfo='skip'
    ))
    
    # Add chokepoints as red X markers
    for point_name, coords in chokepoints.items():
        fig.add_trace(go.Scattergeo(
            lon=[coords['lon']],
            lat=[coords['lat']],
            mode='markers+text',
            marker=dict(
                symbol='x',
                size=15,
                color='red',
                line=dict(width=3, color='red')
            ),
            text=[coords['name']],
            textposition='top center',
            textfont=dict(
                family='Arial',
                size=9,
                color='black'
            ),
            showlegend=False,
            hoverinfo='text',
            hovertext=coords['name']
        ))
    
    # Add traditional route (dashed black line)
    trad_lons = [point[1] for point in traditional_route]
    trad_lats = [point[0] for point in traditional_route]
    
    fig.add_trace(go.Scattergeo(
        lon=trad_lons,
        lat=trad_lats,
        mode='lines',
        line=dict(
            width=2,
            color='black',
            dash='dash'
        ),
        name='Traditional Route: 45-50 days',
        showlegend=True,
        hoverinfo='name'
    ))
    
    # Add NEOM Corridor route (solid blue line)
    neom_lons = [point[1] for point in neom_route]
    neom_lats = [point[0] for point in neom_route]
    
    fig.add_trace(go.Scattergeo(
        lon=neom_lons,
        lat=neom_lats,
        mode='lines',
        line=dict(
            width=2,
            color='blue',
            dash='solid'
        ),
        name='NEOM Corridor Route: 27-32 days',
        showlegend=True,
        hoverinfo='name'
    ))
    
    # Add key country labels
    fig.add_trace(go.Scattergeo(
        lon=[46.6753, 21.0059],
        lat=[24.7136, 44.0165],
        mode='text',
        text=['Saudi Arabia', 'Serbia'],
        textfont=dict(
            family='Arial',
            size=9,
            color='black'
        ),
        showlegend=False,
        hoverinfo='skip'
    ))
    
    # Configure layout with CFR styling
    fig.update_layout(
        title=dict(
            text='Strategic Maritime Routes: Traditional vs NEOM Corridor',
            font=dict(family='Arial', size=14, color='black'),
            x=0.5,
            xanchor='center'
        ),
        geo=dict(
            projection_type='natural earth',
            showland=True,
            landcolor='#E5E5E5',  # Light gray landmasses
            showocean=True,
            oceancolor='white',   # White water
            showlakes=True,
            lakecolor='white',
            showrivers=False,
            showcountries=False,  # No country borders except coastlines
            showcoastlines=True,
            coastlinecolor='#CCCCCC',
            coastlinewidth=0.5,
            showframe=False,
            showsubunits=False,
            lonaxis=dict(range=lon_range),
            lataxis=dict(range=lat_range),
            bgcolor='white'
        ),
        showlegend=True,
        legend=dict(
            x=0.02,
            y=0.98,
            bgcolor='rgba(255,255,255,0.8)',
            bordercolor='black',
            borderwidth=1,
            font=dict(family='Arial', size=9)
        ),
        width=1200,
        height=800,
        margin=dict(l=0, r=0, t=50, b=0),
        paper_bgcolor='white',
        plot_bgcolor='white'
    )
    
    return fig

def export_map(fig, filename='cfr_strategic_maritime_map.png'):
    """Export map as high-resolution PNG and HTML"""
    try:
        # Try to export as PNG with high resolution
        fig.write_image(filename, format='png', width=1200, height=800, scale=2.5)
        print(f"Map exported as {filename}")
    except Exception as e:
        print(f"PNG export failed: {e}")
        print("Kaleido engine not available. Installing plotly-orca or using HTML export instead.")

    # Always save as HTML for interactive viewing
    html_filename = filename.replace('.png', '.html')
    fig.write_html(html_filename)
    print(f"Interactive version saved as {html_filename}")

    # Also create a static HTML version that can be converted to PNG
    static_html = filename.replace('.png', '_static.html')
    fig.write_html(static_html, config={'displayModeBar': False, 'staticPlot': True})
    print(f"Static HTML version saved as {static_html}")

def main():
    """Main function to create and export the CFR-style maritime map"""
    print("Creating CFR-style Strategic Maritime Map...")
    
    # Create the map
    fig = create_cfr_maritime_map()
    
    # Export the map
    export_map(fig)
    
    print("Map creation complete!")
    print("\nMap Features:")
    print("- White background with light gray (#E5E5E5) landmasses")
    print("- Three chokepoints marked with red X symbols")
    print("- Traditional route: black dashed line (45-50 days)")
    print("- NEOM Corridor route: blue solid line (27-32 days)")
    print("- Minimal labels in Arial 9pt font")
    print("- CFR-style professional appearance")
    print("- High-resolution PNG export (300 DPI equivalent)")

if __name__ == "__main__":
    main()
