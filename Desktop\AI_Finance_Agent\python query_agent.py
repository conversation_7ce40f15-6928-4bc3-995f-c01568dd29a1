from transformers import pipeline
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np
import pickle

# Load resources
general_qa = pipeline("question-answering")  # Your fine-tuned general model
finance_qa = pipeline("question-answering", model="yiyanghkust/finbert-tone")  # Finance-specific model
model = SentenceTransformer('all-MiniLM-L6-v2')
index = faiss.read_index("paper_index.faiss")
with open("paper_texts.pkl", "rb") as f:
    paper_texts = pickle.load(f)

def is_finance_query(query):
    # Check if the query is finance-related
    finance_keywords = ["finance", "stock", "market", "investment", "bank", "economy"]
    return any(keyword in query.lower() for keyword in finance_keywords)

def answer_query(query, k=5):
    # Retrieve passages
    query_embedding = model.encode([query])
    D, I = index.search(np.array(query_embedding), k)
    retrieved_passages = [paper_texts[i] for i in I[0]]
    combined_context = " ".join(retrieved_passages)

    # Select the appropriate model
    if is_finance_query(query):
        qa_model = finance_qa
        print("Using finance-specific model...")
    else:
        qa_model = general_qa
        print("Using general model...")

    # Generate one detailed answer
    result = qa_model(question=query, context=combined_context)
    return result['answer']

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python query_agent.py \"Your question here\"")
        sys.exit(1)
    query = sys.argv[1]
    answer = answer_query(query)
    print(f"Answer: {answer}")